export async function handler(event) {
  if (event.httpMethod !== "POST") {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: "Method Not Allowed" }),
    };
  }

  try {
    const { email } = JSON.parse(event.body);

    // Debug: Check environment variables
    console.log("API Key exists:", !!process.env.EMAILOCTOPUS_API_KEY);
    console.log("API Key length:", process.env.EMAILOCTOPUS_API_KEY ? process.env.EMAILOCTOPUS_API_KEY.length : 0);
    console.log("List ID exists:", !!process.env.EMAILOCTOPUS_LIST_ID);
    console.log("List ID:", process.env.EMAILOCTOPUS_LIST_ID);

    // Validate email
    if (!email || !/^[^@]+@[^@]+\.[^@]+$/.test(email)) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: "Invalid email address" }),
      };
    }

    // Check if environment variables are set
    if (!process.env.EMAILOCTOPUS_API_KEY || !process.env.EMAILOCTOPUS_LIST_ID) {
      return {
        statusCode: 500,
        body: JSON.stringify({ 
          error: "Missing environment variables",
          hasApiKey: !!process.env.EMAILOCTOPUS_API_KEY,
          hasListId: !!process.env.EMAILOCTOPUS_LIST_ID
        }),
      };
    }

    // Call EmailOctopus API
    const response = await fetch(
      `https://emailoctopus.com/api/1.6/lists/${process.env.EMAILOCTOPUS_LIST_ID}/contacts`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          api_key: process.env.EMAILOCTOPUS_API_KEY,
          email_address: email,
          status: "SUBSCRIBED",
        }),
      }
    );

    const data = await response.json();

    // Debug: Log the response
    console.log("EmailOctopus response status:", response.status);
    console.log("EmailOctopus response data:", JSON.stringify(data));

    if (!response.ok) {
      return {
        statusCode: response.status,
        body: JSON.stringify({ 
          error: data.error?.message || "Failed to subscribe",
          details: data
        }),
      };
    }

    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, message: "Successfully subscribed!" }),
    };
  } catch (error) {
    console.error("Function error:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: "Server error", details: error.message }),
    };
  }
} 