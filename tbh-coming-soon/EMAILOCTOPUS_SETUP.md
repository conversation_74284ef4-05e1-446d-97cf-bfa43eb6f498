# EmailOctopus Integration Setup

This guide will help you set up EmailOctopus with your "Coming Soon" page.

## Prerequisites

1. An EmailOctopus account (sign up at [emailoctopus.com](https://emailoctopus.com))
2. A mailing list created in EmailOctopus

## Step 1: Get Your EmailOctopus API Key

1. Log in to your EmailOctopus account
2. Go to **Account** → **API Keys**
3. Click **Create API Key**
4. Give it a name (e.g., "Coming Soon Page")
5. Copy the generated API key

## Step 2: Get Your List ID

1. In EmailOctopus, go to **Lists**
2. Click on your mailing list
3. In the URL, you'll see something like: ``
4. The List ID is the part after `/lists/` (e.g., `********-1234-1234-1234-************`)

## Step 3: Configure Environment Variables

1. Create a `.env` file in your project root (if it doesn't exist)
2. Add the following variables:

```env
VITE_EMAILOCTOPUS_API_KEY=eo_baf90e6dd772f0ffac42dbbedd489b831764a9ec3f74612fd3d45f8a3494ad5d
VITE_EMAILOCTOPUS_LIST_ID=
```

**Important:** Replace `your_actual_api_key_here` and `your_actual_list_id_here` with your real values.

## Step 4: Test the Integration

1. Start your development server: `npm run dev`
2. Enter an email address in the "Notify Me" form
3. Submit the form
4. Check your EmailOctopus dashboard to see if the contact was added

## Step 5: Deploy to Production

When deploying to Netlify (or any other platform):

1. Add the environment variables in your hosting platform's settings
2. For Netlify:
   - Go to **Site settings** → **Environment variables**
   - Add `VITE_EMAILOCTOPUS_API_KEY` and `VITE_EMAILOCTOPUS_LIST_ID`
   - Set their values to your actual API key and List ID

## Troubleshooting

### "EmailOctopus configuration is missing" error
- Make sure your `.env` file exists and has the correct variable names
- Ensure the environment variables are set in your hosting platform
- Check that the API key and List ID are correct

### "Failed to subscribe" error
- Verify your API key is valid and has the correct permissions
- Check that your List ID is correct
- Ensure your EmailOctopus account is active

### CORS errors
- EmailOctopus API supports CORS, so this shouldn't be an issue
- If you encounter CORS errors, check your browser's developer console for more details

## Features

The integration includes:

- ✅ Real-time email subscription to EmailOctopus
- ✅ Error handling for invalid emails, already subscribed users, etc.
- ✅ Loading states during submission
- ✅ Success/error message display
- ✅ Automatic form reset after successful submission

## Security Notes

- Never commit your `.env` file to version control
- The API key is exposed to the client-side, but EmailOctopus has rate limiting and other security measures
- Consider implementing additional server-side validation for production use

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your EmailOctopus credentials
3. Test with a simple email address first
4. Check EmailOctopus documentation: [https://emailoctopus.com/api-documentation](https://emailoctopus.com/api-documentation) 