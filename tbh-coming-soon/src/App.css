@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700;900&family=Inter:wght@400;600&family=Montserrat:wght@400;600&display=swap');

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  height: 100%;
  width: 100vw;
  overflow: hidden;
  background: #F8F6F2;
  font-family: 'Inter', 'Montserrat', Arial, sans-serif;
}

.no-scroll {
  height: 100vh;
}

/* App container */
.app, .main-columns {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
}

.app::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.animated-blob {
  position: absolute;
  right: -120px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 0;
  opacity: 0.7;
  pointer-events: none;
  transition: opacity 0.3s;
}

.animated-blob-launch {
  position: absolute;
  right: -120px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 0;
  opacity: 0.8;
  pointer-events: none;
  transition: opacity 0.3s;
  width: 480px;
  height: 480px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.launching-soon-overlay {
  position: absolute;
  left: 50%;
  top: 60%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
  pointer-events: none;
  user-select: none;
}

.rocket-icon {
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 4px 16px rgba(0,0,0,0.12));
}

.launching-text {
  color: #fff;
  font-size: 1.3rem;
  font-weight: 600;
  letter-spacing: 1px;
  text-shadow: 0 2px 10px rgba(0,0,0,0.12);
  opacity: 0.95;
}

.container {
  max-width: 700px;
  width: 100%;
  text-align: center;
  position: relative;
  z-index: 2;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  background: none;
}

/* Logo */
.logo-container {
  margin-bottom: 0.5rem;
  align-self: flex-start;
  display: flex;
  align-items: center;
}

.logo {
  max-width: 90px;
  padding: 0.5rem 0.7rem;
  margin-bottom: 0;
  margin-right: 1.2rem;
  vertical-align: middle;
  background: white;
  border-radius: 12px;
}

.logo-transparent {
  opacity: 0.7;
  background: transparent;
}

/* Headline */
.headline {
  text-align: left;
  align-self: flex-start;
  display: inline-block;
  vertical-align: middle;
  margin-top: 0;
  font-size: clamp(1.5rem, 5vw, 2.8rem);
  font-weight: 700;
  line-height: 1.2;
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Subheadline */
.subheadline {
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  font-weight: 400;
  line-height: 1.5;
}

/* Coming Soon Badge */
.coming-soon-badge {
  margin-bottom: 2rem;
}

.badge-text {
  display: inline-block;
  padding: 0.7rem 1.5rem;
  border-radius: 50px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

/* Email Signup */
.email-signup {
  margin-bottom: 2rem;
  width: 100%;
}

.email-signup h3 {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
  font-weight: 500;
}

.email-form {
  width: 100%;
  display: flex;
  justify-content: center;
}

.input-group {
  display: flex;
  align-items: center;
  background: #f7f5f2;
  border-radius: 1.2rem;
  box-shadow: 0 2px 12px rgba(34,34,34,0.04);
  padding: 0.18rem 0.18rem;
  gap: 0.2rem;
}

.email-input {
  border: none;
  outline: none;
  background: transparent;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  min-width: 180px;
  max-width: 220px;
  height: 2.2rem;
}

.email-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.submit-btn {
  background: #B03A2E;
  color: #fff;
  font-weight: 600;
  border-radius: 1rem;
  padding: 0.5em 1.2em;
  font-size: 1rem;
  min-width: 90px;
  height: 2.2rem;
  box-shadow: 0 2px 8px rgba(176,58,46,0.07);
  transition: background 0.2s, box-shadow 0.2s;
  margin-left: 0.2rem;
}

.submit-btn:hover:not(:disabled) {
  background: #922B21;
  box-shadow: 0 4px 16px rgba(176,58,46,0.18);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #eee;
  border-top: 2px solid #B85C43;
  border-radius: 50%;
}

/* Success Message */
.success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #4ade80;
  font-weight: 500;
}

.success-icon {
  color: #4ade80;
}

/* Social Links */
.social-links {
  display: flex;
  justify-content: center;
  gap: 1.2rem;
  margin-bottom: 1.2rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Footer */
.footer {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-top: 1.2rem;
}

.email-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.email-link:hover {
  color: #4ade80;
}

/* Mobile hide */
.mobile-hide {
  display: inline;
}

/* Responsive Design */
@media (max-width: 1100px) {
  .animated-blob {
    right: -200px;
    width: 320px !important;
    height: 320px !important;
  }
  .animated-blob-launch {
    right: -200px;
    width: 320px !important;
    height: 320px !important;
  }
}

@media (max-width: 900px) {
  .animated-blob {
    right: -260px;
    width: 220px !important;
    height: 220px !important;
    opacity: 0.4;
  }
  .animated-blob-launch {
    right: -260px;
    width: 220px !important;
    height: 220px !important;
    opacity: 0.4;
  }
  .container {
    min-height: 90vh;
  }
}

@media (max-width: 700px) {
  html, body, .app, .main-columns {
    overflow-x: hidden;
    overflow-y: auto;
    height: auto;
    min-height: 100vh;
  }
  .animated-blob {
    display: none;
  }
  .animated-blob-launch {
    display: none;
  }
  .container {
    min-height: 100vh;
    padding: 0 0.5rem;
  }
  .logo {
    max-width: 60px;
    padding: 0.3rem 0.5rem;
    margin-right: 0.7rem;
  }
  .logo-container {
    justify-content: center;
  }
  .headline {
    text-align: center;
    align-self: center;
    margin-top: 0;
  }
  .mobile-hide {
    display: none;
  }
  .tbh-logo-img {
    max-width: 90vw;
    width: 260px;
    min-width: 120px;
    padding: 0.5rem 0.7rem;
    display: block;
    margin: 2.2rem auto 1.2rem auto;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.2rem;
  }
  .headline {
    font-size: 1rem;
  }
  .subheadline {
    font-size: 0.95rem;
  }
  .badge-text {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
  .email-signup h3 {
    font-size: 0.95rem;
  }
  .logo {
    max-width: 70px;
    padding: 0.7rem 0.8rem;
  }
  .tbh-logo-img {
    max-width: 80vw;
    width: 200px;
    min-width: 100px;
    padding: 0.3rem 0.5rem;
    margin: 1.5rem auto 0.7rem auto;
  }
}

/* Animation keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Additional hover effects */
.logo:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.badge-text:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.main-columns {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  max-width: 100vw;
  gap: 0;
}

.left-column {
  flex: 1 1 0;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  height: 100%;
  z-index: 2;
}

.content-group {
  width: 100%;
  max-width: 540px;
  margin: 0 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 1.2rem;
}

.logo-container {
  margin-bottom: 1.2rem;
  align-self: flex-start;
}

.logo {
  max-width: 110px;
  padding: 1rem 1.2rem;
  margin-bottom: 0;
}

.headline {
  text-align: left;
  align-self: flex-start;
}

.subheadline, .coming-soon-badge, .email-signup, .social-links, .footer {
  align-self: flex-start;
}

.right-column {
  flex: 1 1 0;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  z-index: 1;
  position: relative;
}

.animated-blob-launch {
  margin-left: 0;
  margin-right: auto;
  position: relative;
  right: 0;
  top: 0;
  transform: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.launching-soon-overlay {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
  pointer-events: none;
  user-select: none;
}

@media (max-width: 1100px) {
  .main-columns {
    gap: 0;
  }
  .animated-blob-launch {
    width: 320px !important;
    height: 320px !important;
  }
}
@media (max-width: 900px) {
  .main-columns {
    gap: 0;
  }
  .animated-blob-launch {
    width: 220px !important;
    height: 220px !important;
    opacity: 0.4;
  }
}
@media (max-width: 700px) {
  .main-columns {
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    height: auto;
    min-height: 100vh;
  }
  .left-column, .content-group {
    align-items: center;
    width: 100%;
    max-width: 100vw;
    margin: 0 auto;
  }
  .right-column {
    display: none;
  }
  .headline, .subheadline, .coming-soon-badge, .email-signup, .social-links, .footer {
    align-self: center;
    text-align: center;
  }
  .logo-container {
    align-self: center;
  }
}

.hero-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 80vh;
  gap: 1.2rem;
}

.hero-headline {
  max-width: 480px;
  margin: 0 auto 1.2rem auto;
  text-align: center;
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  line-height: 1.15;
}

.logo-container {
  margin-bottom: 1.2rem;
  align-self: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  max-width: 120px;
  padding: 1rem 1.2rem;
  margin-bottom: 0;
  margin-right: 0;
  vertical-align: middle;
  background: white;
  border-radius: 12px;
}

@media (max-width: 700px) {
  .hero-center {
    min-height: 60vh;
    gap: 0.7rem;
  }
  .hero-headline {
    max-width: 95vw;
    font-size: 1.1rem;
  }
  .logo {
    max-width: 70px;
    padding: 0.5rem 0.7rem;
  }
}

.hero-app {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-blob-bg {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  pointer-events: none;
  opacity: 0.7;
}

.hero-center-col {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 540px;
  margin: 0 auto;
  min-height: 90vh;
  gap: 1.2rem;
}

.logo-container {
  margin-bottom: 1.2rem;
  align-self: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  max-width: 120px;
  padding: 1rem 1.2rem;
  margin-bottom: 0;
  margin-right: 0;
  vertical-align: middle;
  background: white;
  border-radius: 12px;
  opacity: 0.7;
}

.hero-headline {
  max-width: 480px;
  margin: 0 auto 1.2rem auto;
  text-align: center;
  font-size: clamp(1.3rem, 4vw, 2.1rem);
  font-weight: 900;
  color: #222;
  letter-spacing: 0.01em;
  background: linear-gradient(45deg, #fff, #e0e0e0 80%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subheadline {
  font-size: 1.2rem;
  color: rgba(255,255,255,0.92);
  margin-bottom: 1.5rem;
  font-weight: 400;
  text-align: center;
  line-height: 1.5;
}

.hero-launching-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.2rem;
  gap: 0.2rem;
}

.rocket-icon {
  margin-bottom: 0.2rem;
  filter: drop-shadow(0 4px 16px rgba(0,0,0,0.12));
}

.launching-text {
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 1px;
  text-shadow: 0 2px 10px rgba(0,0,0,0.12);
  opacity: 0.95;
}

.email-signup {
  margin-bottom: 1.2rem;
  width: 100%;
}

.email-signup h3 {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
  font-weight: 500;
  text-align: center;
}

.email-form {
  max-width: 400px;
  margin: 0 auto;
}

.input-group {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.email-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 1rem 1.2rem;
  color: white;
  font-size: 1rem;
  outline: none;
}

.email-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.submit-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 50px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  min-width: 110px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
}

.success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #4ade80;
  font-weight: 500;
}

.success-icon {
  color: #4ade80;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1.2rem;
  margin-bottom: 1.2rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.footer {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-top: 1.2rem;
  text-align: center;
}

.email-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.email-link:hover {
  color: #4ade80;
}

@media (max-width: 900px) {
  .hero-blob-bg svg {
    width: 400px !important;
    height: 400px !important;
  }
  .hero-center-col {
    min-height: 80vh;
    max-width: 95vw;
  }
}
@media (max-width: 700px) {
  html, body, .hero-app {
    overflow-x: hidden;
    overflow-y: auto;
    height: auto;
    min-height: 100vh;
  }
  .hero-blob-bg svg {
    width: 250px !important;
    height: 250px !important;
  }
  .hero-center-col {
    min-height: 60vh;
    gap: 0.7rem;
    max-width: 99vw;
  }
  .hero-headline {
    max-width: 95vw;
    font-size: 1.1rem;
  }
  .logo {
    max-width: 70px;
    padding: 0.5rem 0.7rem;
  }
}

.modern-hero-bg {
  min-height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.modern-hero-overlay {
  position: absolute;
  inset: 0;
  background: rgba(20, 24, 40, 0.55);
  z-index: 1;
  pointer-events: none;
}

.modern-hero-logo {
  position: absolute;
  top: 3.5rem;
  left: 3.5rem;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  max-width: 90px;
  background: white;
  border-radius: 16px;
  opacity: 0.92;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  padding: 0.7rem 1.2rem;
  transition: box-shadow 0.2s, opacity 0.2s;
}

.modern-hero-socials {
  position: absolute;
  left: 2.5rem;
  bottom: 2.5rem;
  z-index: 3;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.social-link {
  color: #fff;
  opacity: 0.8;
  background: rgba(255,255,255,0.08);
  border-radius: 50%;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, opacity 0.2s;
  border: 1px solid rgba(255,255,255,0.15);
}
.social-link:hover {
  background: rgba(255,255,255,0.18);
  opacity: 1;
}

.modern-hero-center {
  position: relative;
  z-index: 4;
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
}

.modern-hero-plane {
  margin-bottom: 0.5rem;
}

.modern-hero-headline {
  font-size: clamp(2.5rem, 7vw, 4.5rem);
  font-weight: 900;
  color: #fff;
  text-align: center;
  letter-spacing: 0.04em;
  margin-bottom: 0.5rem;
  text-shadow: 0 4px 32px rgba(0,0,0,0.12);
  position: relative;
}

.modern-hero-underline {
  width: 120px;
  height: 5px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  margin: 0 auto 1.2rem auto;
  transform-origin: left;
}

.modern-hero-subheadline {
  color: #e0e0e0;
  font-size: 1.2rem;
  text-align: center;
  font-weight: 400;
  line-height: 1.5;
  margin-bottom: 1.2rem;
}

.modern-hero-email-signup {
  width: 100%;
  max-width: 420px;
  margin: 0 auto 1.2rem auto;
  text-align: center;
}

.email-form {
  max-width: 420px;
  margin: 0 auto;
}
.input-group {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
.email-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 1rem 1.2rem;
  color: white;
  font-size: 1rem;
  outline: none;
}
.email-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}
.submit-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 50px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  min-width: 110px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}
.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
}
.success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #4ade80;
  font-weight: 500;
}
.success-icon {
  color: #4ade80;
}
.modern-hero-footer {
  position: absolute;
  left: 50%;
  bottom: 2.5rem;
  transform: translateX(-50%);
  color: #e0e0e0;
  font-size: 0.95rem;
  z-index: 3;
  text-align: center;
}
.email-link {
  color: #fff;
  text-decoration: underline;
  font-weight: 500;
  transition: color 0.3s ease;
}
.email-link:hover {
  color: #4ade80;
}
@media (max-width: 900px) {
  .modern-hero-logo, .modern-hero-socials {
    left: 1rem;
    top: 1rem;
    bottom: 1rem;
  }
  .modern-hero-footer {
    left: 50%;
    bottom: 1rem;
    font-size: 0.9rem;
  }
}
@media (max-width: 700px) {
  html, body, .modern-hero-bg {
    overflow-x: hidden;
    overflow-y: auto;
    height: auto;
    min-height: 100vh;
  }
  .modern-hero-logo {
    top: 0.7rem;
    left: 0.7rem;
  }
  .modern-hero-socials {
    left: 0.7rem;
    bottom: 0.7rem;
    gap: 0.7rem;
  }
  .modern-hero-center {
    min-height: 80vh;
    gap: 1rem;
    padding: 0 0.5rem;
  }
  .modern-hero-headline {
    font-size: 1.5rem;
    max-width: 95vw;
  }
  .logo {
    max-width: 40px;
    padding: 0.3rem 0.5rem;
  }
}

.tbh-hero-bg {
  min-height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
  background: #F8F6F2;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.tbh-logo-img {
  display: block;
  margin: 2.2rem auto 1.2rem auto;
  max-width: 260px;
  width: 100%;
  height: auto;
  background: none;
  border: none;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.tbh-email-headline {
  color: #222;
  font-size: 1.15rem;
  font-weight: 600;
  margin-bottom: 1.2rem;
  text-align: center;
  font-family: 'Inter', 'Montserrat', Arial, sans-serif;
}

.tbh-hero-center {
  position: relative;
  z-index: 4;
  width: 100vw;
  min-height: 60vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 0.7rem;
  padding: 0 1.2rem;
}

.tbh-hero-headline {
  font-family: 'Playfair Display', serif;
  font-size: clamp(1.3rem, 4vw, 2.1rem);
  font-weight: 900;
  color: #222;
  text-align: center;
  letter-spacing: 0.01em;
  margin-bottom: 0.3rem;
  margin-top: 0;
  line-height: 1.08;
}

.tbh-hero-underline {
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #B85C43 0%, #222 100%);
  border-radius: 3px;
  margin: 0 auto 2.2rem auto;
  transform-origin: left;
}

.tbh-hero-subheadline {
  color: #444;
  font-size: 1.1rem;
  text-align: center;
  font-family: 'Inter', 'Montserrat', Arial, sans-serif;
  font-weight: 400;
  line-height: 1.5;
  margin-bottom: 1.2rem;
  margin-top: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.tbh-hero-email-signup {
  width: 100%;
  max-width: 420px;
  margin: 0 auto 1.8rem auto;
  text-align: center;
}

.email-form {
  max-width: 420px;
  margin: 0 auto;
}

.input-group {
  display: flex;
  gap: 0.5rem;
  background: rgba(34, 34, 34, 0.04);
  border-radius: 50px;
  padding: 0.5rem;
  border: 1px solid #eee;
  box-shadow: 0 4px 16px rgba(34,34,34,0.04);
}

.email-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 1rem 1.2rem;
  color: #222;
  font-size: 1rem;
  outline: none;
  font-family: 'Inter', 'Montserrat', Arial, sans-serif;
}

.email-input::placeholder {
  color: #888;
}

.submit-btn {
  background: #B03A2E;
  color: #fff;
  font-weight: 600;
  border-radius: 2rem;
  padding: 0.7em 2.2em;
  font-size: 1.1em;
  box-shadow: 0 2px 8px rgba(176,58,46,0.07);
  transition: background 0.2s, box-shadow 0.2s;
}

.submit-btn:hover:not(:disabled) {
  background: #922B21;
  box-shadow: 0 4px 16px rgba(176,58,46,0.18);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #eee;
  border-top: 2px solid #B85C43;
  border-radius: 50%;
}

.success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #4ade80;
  font-weight: 500;
}

.success-icon {
  color: #4ade80;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #ef4444;
  font-weight: 500;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.error-icon {
  color: #ef4444;
  flex-shrink: 0;
}

.tbh-hero-socials {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin: -0.8rem auto 0.5rem auto;
  position: static;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: #fff6f3;
  box-shadow: 0 2px 8px rgba(176,58,46,0.07);
  color: #B03A2E;
  font-size: 28px;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}

.social-link:hover {
  background: #B03A2E;
  color: #fff;
  box-shadow: 0 4px 16px rgba(176,58,46,0.18);
}

.tbh-hero-footer {
  margin-top: 1.5rem;
  padding-bottom: 2.5rem;
  text-align: center;
  font-size: 1rem;
  color: #888;
  background: none;
}

.email-link {
  color: #B03A2E;
  font-weight: 600;
  text-decoration: underline dotted;
  transition: color 0.2s;
}

.email-link:hover {
  color: #922B21;
}

@media (max-width: 900px) {
  .tbh-hero-logo-img {
    margin-top: 2rem;
    margin-bottom: 1.2rem;
  }
  .tbh-logo-img {
    max-width: 180px;
    margin: 1.5rem auto 1rem auto;
  }
  .tbh-hero-footer, .tbh-hero-socials {
    bottom: 0.7rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 700px) {
  html, body, .tbh-hero-bg {
    overflow-x: hidden;
    overflow-y: auto;
    height: auto;
    min-height: 100vh;
  }
  .tbh-hero-logo-img {
    margin-top: 1.2rem;
    margin-bottom: 0.7rem;
  }
  .tbh-logo-img {
    max-width: 120px;
    margin: 1rem auto 0.7rem auto;
  }
  .tbh-hero-center {
    min-height: 60vh;
    gap: 1rem;
    padding: 0 0.5rem;
  }
  .tbh-hero-headline {
    font-size: 1.1rem;
    margin-bottom: 0.2rem;
  }
  .tbh-hero-underline {
    margin-bottom: 1.2rem;
  }
  .tbh-hero-subheadline {
    font-size: 0.95rem;
    margin-bottom: 0.7rem;
  }
  .tbh-hero-socials {
    margin-top: 2.2rem;
    margin-bottom: 0.2rem;
  }
  .tbh-hero-footer {
    padding-bottom: 1.5rem;
  }
}

/* --- Animated Rocket Illustrations (Two Rockets, Flying Across Page) --- */
.tbh-hero-rocket {
  position: absolute;
  z-index: 1;
  pointer-events: none;
  opacity: 0.96;
}
.tbh-hero-rocket-1 {
  left: 0;
  bottom: 0;
}
.tbh-hero-rocket-2 {
  right: 0;
  top: 0;
  opacity: 0.92;
}
.rocket-trail {
  pointer-events: none;
  z-index: 0;
}
@media (max-width: 900px) {
  .tbh-hero-rocket-1, .tbh-hero-rocket-2 {
    width: 32px;
    height: 32px;
  }
}
@media (max-width: 700px) {
  .tbh-hero-rocket {
    display: none;
  }
}

/* --- Animated Abstract Blob Background Accents (Two Blobs) --- */
.tbh-hero-blob-bg {
  position: absolute;
  z-index: 0;
  pointer-events: none;
  opacity: 0.85;
}
.tbh-hero-blob-bg-1 {
  right: 0;
  bottom: 0;
}
.tbh-hero-blob-bg-2 {
  left: 0;
  top: 0;
  opacity: 0.7;
}
.tbh-hero-illustration {
  position: absolute;
  left: 8vw;
  top: 38vh;
  z-index: 0;
  pointer-events: none;
  opacity: 0.14;
}
@media (max-width: 900px) {
  .tbh-hero-blob-bg-1 {
    width: 160px;
    height: 80px;
  }
  .tbh-hero-blob-bg-2 {
    width: 100px;
    height: 60px;
    left: 0;
    top: 0;
  }
  .tbh-hero-illustration {
    width: 60px;
    height: 60px;
    left: 2vw;
    top: 30vh;
  }
}
@media (max-width: 700px) {
  .tbh-hero-blob-bg, .tbh-hero-illustration {
    display: none;
  }
}

.tbh-hero-footer-email {
  margin-top: 0.7rem;
  font-size: 1.08rem;
  font-weight: 600;
  color: #B03A2E;
  letter-spacing: 0.01em;
  text-align: center;
  background: none;
}
.tbh-hero-footer-email .email-link {
  color: #B03A2E;
  font-weight: 700;
  text-decoration: underline dotted;
  transition: color 0.2s;
}
.tbh-hero-footer-email .email-link:hover {
  color: #922B21;
}

.logo-veil {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 220px;
  height: 140px;
  border-radius: 50%;
  background: radial-gradient(circle, #fde6e1 0%, #fff8f3 60%, rgba(255,255,255,0.01) 100%);
  filter: blur(12px);
  z-index: 0;
  pointer-events: none;
  opacity: 0.8;
}
@media (max-width: 700px) {
  .logo-veil {
    width: 120px;
    height: 70px;
    filter: blur(8px);
  }
}

/* --- Playful Cooking Illustrations as Background Accents --- */
.tbh-hero-cook-illustration {
  position: absolute;
  z-index: 0;
  pointer-events: none;
}
.chef-hat {
  left: 4vw;
  top: 3vh;
}
.spoon {
  right: 6vw;
  top: 18vh;
}
.egg {
  right: 10vw;
  bottom: 6vh;
}
@media (max-width: 900px) {
  .chef-hat {
    left: 1vw;
    top: 1vh;
    width: 50px;
    height: 40px;
  }
  .spoon {
    right: 2vw;
    top: 10vh;
    width: 36px;
    height: 72px;
  }
  .egg {
    right: 2vw;
    bottom: 2vh;
    width: 40px;
    height: 40px;
  }
}
@media (max-width: 700px) {
  .tbh-hero-cook-illustration {
    display: none;
  }
}

/* --- Animated Gradient Blob from Top Right to Bottom --- */
.tbh-hero-gradient-blob {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 0;
  pointer-events: none;
  opacity: 0.85;
}

/* --- Construction Illustrations as Background Accents --- */
.tbh-hero-construct-illustration {
  position: absolute;
  z-index: 0;
  pointer-events: none;
}
.cone {
  left: 6vw;
  bottom: 8vh;
}
.barrier {
  right: 8vw;
  top: 12vh;
}
.wrench {
  left: 10vw;
  top: 38vh;
}
@media (max-width: 900px) {
  .tbh-hero-gradient-blob {
    width: 400px;
    height: 300px;
  }
  .cone {
    left: 2vw;
    bottom: 2vh;
    width: 36px;
    height: 36px;
  }
  .barrier {
    right: 2vw;
    top: 6vh;
    width: 54px;
    height: 30px;
  }
  .wrench {
    left: 2vw;
    top: 20vh;
    width: 32px;
    height: 32px;
  }
}
@media (max-width: 700px) {
  .tbh-hero-gradient-blob, .tbh-hero-construct-illustration {
    display: none;
  }
}

/* --- Gently Animated Blobs Inspired by Reference Image --- */
.tbh-hero-bg-blob {
  position: absolute;
  z-index: 0;
  pointer-events: none;
}
.blob-top-left {
  left: 0;
  top: 0;
}
.blob-bottom-left {
  left: 0;
  bottom: 0;
}
.blob-bottom-right {
  right: 0;
  bottom: 0;
}
@media (max-width: 900px) {
  .blob-top-left {
    width: 220px;
    height: 300px;
  }
  .blob-bottom-left {
    width: 260px;
    height: 180px;
  }
  .blob-bottom-right {
    width: 200px;
    height: 120px;
  }
}
@media (max-width: 700px) {
  .tbh-hero-bg-blob {
    display: none;
  }
}

/* --- GLASSY LAUNCH BADGE --- */
.tbh-glass-launch-badge {
  display: inline-block;
  margin: 0.2rem auto 1.1rem auto;
  padding: 0.7rem 2.2rem;
  border-radius: 1.7rem;
  background: #fff6f3;
  box-shadow: 0 2px 8px rgba(176,58,46,0.07);
  border: 1.5px solid #f3e0db;
  font-family: 'Inter', 'Montserrat', Arial, sans-serif;
  font-size: 1.25rem;
  font-weight: 700;
  color: #B03A2E;
  text-align: center;
  letter-spacing: 0.01em;
  position: relative;
  overflow: hidden;
}

.tbh-hero-underline {
  display: none;
}
